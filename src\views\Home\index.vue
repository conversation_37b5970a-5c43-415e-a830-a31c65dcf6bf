<template>
  <div class="home-page">
    <!-- 主要内容 -->
    <main class="page-content">
      <!-- 轮播图 -->
      <HeroSlider
        :slides="slides"
        @learn-more="handleLearnMore"
        @slide-change="handleSlideChange"
      />

      <!-- 公司简介模块 -->
      <section class="company-intro" id="集团简介">
        <div class="container">
          <div class="intro-layout">
            <!-- 左侧公司简介 -->
            <div class="intro-left">
              <div class="company-logo">
                <img src="/uploads/sites/1012/2022/11/18b3630d73e2d35aa818fdedb18cd8e0.png" alt="公司标识" />
              </div>
              <div class="intro-content">
                <p class="intro-subtitle">WHO WE ARE</p>
                <h2 class="intro-title">數字起源 (香港) 有限公司</h2>
                <div class="intro-divider">
                  <img src="/uploads/sites/1012/2022/11/15d5fcbdf0b8f197d7a8bb0f076ad841.jpg" alt="分割线" width="101" height="6" />
                </div>
                <div class="intro-description">
                  <p class="intro-text">
                    系環球數科股份有限公司（以下簡稱"集團"）下屬機構，作為集團海外總部、創新研發中心、全球運營中心，
                    公司將基於集團 20 餘年的行業沉澱及技術積累，依托香港在區位、政策、人才等方面的優勢，
                    聚焦前沿技術研究、技術驗證、產品預研及海外市場拓展，形成國內與海外市場雙循環的出海新格局。
                    數字起源，連接無限可能，賦能美好生活。
                  </p>
                </div>
                <div class="view-more-section">
                  <button class="view-more-btn" @click="navigateTo('/about-us')">
                    查看更多 >>
                  </button>
                </div>
              </div>
            </div>

            <!-- 右侧统计数据 -->
            <div class="intro-right">
              <div class="statistics-grid">
                <div class="stat-card hvr-float custom-yy">
                  <div class="stat-number">
                    <span class="counter" data-target="24">24</span>
                    <span class="plus">+</span>
                  </div>
                  <h3>行業理解</h3>
                  <p>創新構建"AI 大腦+PaaS 引擎+SaaS 產品"的"雲智鏈一體"行業解決方案及全棧技術能力。</p>
                </div>
                <div class="stat-card hvr-float custom-yy">
                  <div class="stat-number">
                    <span class="counter" data-target="6">6</span>
                    <span class="plus">+</span>
                  </div>
                  <h3>6 大技術支持落地</h3>
                  <p>廣泛佈局前沿領域，加速推動 AI 在各垂直場景中的應用落地，為行業賦能。</p>
                </div>
                <div class="stat-card hvr-float custom-yy">
                  <div class="stat-number">
                    <span class="counter" data-target="3">3</span>
                    <span class="plus">+X</span>
                  </div>
                  <h3>場景落地</h3>
                  <p>以先發行業智慧文旅為起點，有序擴展智慧生態和城市服務領域。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 智慧生态模块 -->
      <section class="smart-ecology">
        <div class="container">
          <div class="section-header">
            <h2>智慧生態</h2>
            <p class="section-subtitle">運用先進技術構建智慧生態監測與管理體系</p>
          </div>
          <div class="ecology-grid">
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/monitoring')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/073346750cbf5e05e3ea59df9549578a.png" alt="生態監測" />
              </div>
              <h3>生態監測</h3>
              <p>實時監測生態環境變化，提供科學數據支撐</p>
            </div>
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/warning')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/d1fba75503642fc884e617bc2a146ff9.png" alt="災害預警" />
              </div>
              <h3>災害預警</h3>
              <p>智能預警系統，及時發現潛在風險</p>
            </div>
            <div class="ecology-item pulse-hover" @click="navigateTo('/smart-ecology/fire-prevention')">
              <div class="item-icon">
                <img src="/uploads/sites/1012/2022/11/b12e5684196204befd31d5c43ff6aae3.png" alt="森林防火" />
              </div>
              <h3>森林防火</h3>
              <p>全方位森林火災防控解決方案</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 人工智能模块 -->
      <section class="ai-section">
        <div class="container">
          <div class="section-header">
            <h2>人工智能</h2>
            <p class="section-subtitle">領先的AI技術與解決方案</p>
          </div>
          <div class="ai-content">
            <div class="ai-category">
              <h3>垂類大模型</h3>
              <div class="ai-grid">
                <div class="ai-item" @click="navigateTo('/ai/multimodal')">
                  <img src="/uploads/sites/1012/2024/07/83841dc18fcc7f97315235a2ac571d46.png" alt="多模態融合" />
                  <span>多模態融合</span>
                  <p>整合文本、圖像、語音等多種數據模態</p>
                </div>
                <div class="ai-item" @click="navigateTo('/ai/training')">
                  <img src="/uploads/sites/1012/2024/07/34cf3b25d4852b8410937873b5be53f8.png" alt="大模型訓練" />
                  <span>大模型訓練</span>
                  <p>專業的大規模模型訓練服務</p>
                </div>
                <div class="ai-item" @click="navigateTo('/ai/nlp')">
                  <img src="/uploads/sites/1012/2024/07/b6ac1fea464d47ee66ddcc796b92268e.png" alt="自然語言處理" />
                  <span>自然語言處理</span>
                  <p>先進的NLP技術與應用</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      <!-- 新闻中心 -->
      <section class="news-center">
        <div class="container">
          <div class="section-header">
            <p class="section-subtitle">NEWS CENTER</p>
            <h2>新聞中心</h2>
          </div>
          <div class="news-list">
            <div class="news-item">
              <div class="news-image">
                <img src="/uploads/sites/1012/2024/08/c4a51cfaac61c49721e8aac314b36b58.png" alt="新闻图片" />
              </div>
              <div class="news-content">
                <h3>行業研究 I 我國生成式人工智能拐點探究</h3>
              </div>
            </div>
          </div>
          <button class="view-more-btn">查看更多 >></button>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import HeroSlider from '@/components/common/HeroSlider.vue'

// 响应式数据
const router = useRouter()

const slides = ref([
  {
    background: '/uploads/sites/1012/2022/11/a28adbebd944acf1203439567578dc64.jpg',
    logo: '/uploads/sites/1012/2024/12/49894a4535b5c9c8e99b1e377f624758.png',
    title: '數字起源',
    description: '以數字科技為基礎、以科技創新賦能美好生活',
    link: '/about'
  },
  {
    background: '/uploads/sites/1012/2022/11/文旅.jpg',
    logo: '/uploads/sites/1012/2022/12/8c2ec5739cfdd126fc68f6470b455444.png',
    title: '智慧文旅',
    description: '首創基於聯盟鏈應用的全場景目的地雲服務解決方案，以"智旅鏈 + 慧旅雲"雙擎驅動旅遊目的地數字化進程。',
    link: '/smart-culturetourism'
  },
  {
    background: '/uploads/sites/1012/2022/11/002c39e262ac1571f6e62d7f1c7bccdf.jpg',
    logo: '/uploads/sites/1012/2024/12/0ebb375deb8d0be40a02738f15abe603.png',
    title: '智慧城市',
    description: '創新構建基於 AI 城市大腦的"一屏觀城鄉""一網管全域""一語惠全城"基礎模塊，特色打造四大維度智慧應用。',
    link: '/smart-city'
  },
  {
    background: '/uploads/sites/1012/2022/11/b3179e560087fe203f3917d9fcfc0ea7.jpg',
    logo: '/uploads/sites/1012/2022/12/d19ea5a2314a580bbee680fbeade2000.png',
    title: '智慧生態',
    description: '面向自然保護地典型場景，採用"七橫兩縱"邏輯架構，六大模塊全流程賦能智慧生態數字化。',
    link: '/smart-ecology'
  },
  {
    background: '/uploads/sites/1012/2024/07/d508f04c957ff3b6dc0beb7539f56d2b.jpg',
    logo: '/uploads/sites/1012/2024/09/f63e538e9aa6f6e80699de56ee275ac7.png',
    title: 'AIGC應用',
    description: '創新構建基於AIGC的諮詢服務、行程規劃、導遊導覽、智能營銷、品牌推廣、輿情管理等智慧化應用體系。',
    link: '/aigc-applications'
  },
  {
    background: '/uploads/sites/1012/2024/07/268767b6fb7fadf7e59c6fe749925588.jpg',
    logo: '/uploads/sites/1012/2024/09/e11c105c07df6a8d776b21d6756060ef.png',
    title: '數金雲/數科雲',
    description: '公司持續加大研發投入，在數金雲、數科雲、區塊鏈應用等通用方面的產品和服務日益成熟，為眾多行業用戶賦能增效。',
    link: '/digital-cloud'
  }
])

// 方法
const handleLearnMore = (slide) => {
  router.push(slide.link)
}

const handleSlideChange = (slideIndex) => {
  // 可以在这里处理幻灯片切换事件，比如统计或其他逻辑
  console.log('当前幻灯片索引:', slideIndex)
}

const navigateTo = (path) => {
  router.push(path)
}

const initCounters = () => {
  // 数字计数动画
  const animateCounter = (counter) => {
    const target = parseInt(counter.getAttribute('data-target'))
    const increment = target / 100
    let current = 0

    const updateCounter = () => {
      if (current < target) {
        current += increment
        counter.textContent = Math.ceil(current)
        requestAnimationFrame(updateCounter)
      } else {
        counter.textContent = target
      }
    }

    updateCounter()
  }

  // 使用 Intersection Observer 来触发动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const counter = entry.target.querySelector('.counter')
        if (counter && !counter.classList.contains('animated')) {
          counter.classList.add('animated')
          animateCounter(counter)
        }
      }
    })
  })

  document.querySelectorAll('.stat-card').forEach(card => {
    observer.observe(card)
  })
}

// 生命周期钩子
onMounted(() => {
  initCounters()
})
</script>

<style scoped>
/* 基础样式将在单独的样式文件中定义 */
@import './styles.css';
</style>
